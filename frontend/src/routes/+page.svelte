<script lang="ts">
  import { onMount } from 'svelte';
  import ThemeToggle from '$components/ui/ThemeToggle.svelte';
  import PersonaSelector from '$components/PersonaSelector.svelte';
  import EvaluationDisplay from '$components/EvaluationDisplay.svelte';
  import type {
    CBTEvaluationResult,
    ComparativeCBTEvaluationResult
  } from '../types/index.js';

  let wsConnection: WebSocket | null = null;
  let connectionStatus = 'Disconnected';
  let messages: Array<{
    sender: 'therapist' | 'patient';
    content: string;
    timestamp: string;
    thinking?: string;
    metadata?: any;
  }> = [];
  let therapistThoughts: Array<{ content: string; timestamp: string }> = [];
  let patientThoughts: Array<{ content: string; timestamp: string }> = [];

  // Profile data - will be fetched from API
  let therapistProfile = {
    name: "Loading...",
    credentials: "",
    specialization: "",
    approach: "",
    experience: "",
    sessions: ""
  };

  let patientProfile = {
    name: "",
    age: "",
    sessionHistory: "",
    background: "",
    traits: "",
    lastSession: ""
  };

  // Selected persona details for display
  let selectedPersonaDetails: any = null;

  // Analytics data - tracks pre and post conversation metrics
  let preConversationAnalytics = {
    sentiment: null,
    motivationLevel: null,
    engagementLevel: null
  };

  let postConversationAnalytics = {
    sentiment: null,
    motivationLevel: null,
    engagementLevel: null
  };

  // Dummy messages for testing - updated to match backend structure
  // messages = [
  //   {
  //     sender: 'therapist',
  //     content: 'Hello, I\'m Dr. Sila. I\'m glad you\'re here today. How are you feeling right now?',
  //     timestamp: new Date().toISOString(),
  //     thinking: 'Patient seems anxious. Need to establish rapport and create safe space.',
  //     metadata: {
  //       confidence: 0.9,
  //       processingTime: 1250,
  //       patientAnalysis: {
  //         sentiment: 'neutral',
  //         sentimentIntensity: 'medium',
  //         motivationLevel: 'medium',
  //         motivationType: 'mixed',
  //         engagementLevel: 'medium',
  //         engagementPatterns: ['hesitant', 'observant'],
  //         readinessScore: {
  //           score: 6,
  //           recommendedApproach: 'MI',
  //           reasoning: 'Patient shows moderate engagement but appears guarded. MI approach recommended to build motivation.',
  //           indicators: {
  //             positive: ['present', 'responsive'],
  //             negative: ['guarded', 'uncertain']
  //           }
  //         }
  //       },
  //       therapeuticApproach: {
  //         name: 'Motivational Interviewing',
  //         selectedTechnique: {
  //           name: 'Open-ended Questions',
  //           description: 'Using open-ended questions to encourage patient expression and exploration'
  //         }
  //       }
  //     }
  //   },
  //   {
  //     sender: 'patient',
  //     content: 'I\'m... okay, I guess. A bit nervous about being here.',
  //     timestamp: new Date().toISOString(),
  //     thinking: 'This feels awkward. Not sure what to expect. Should I trust this person?',
  //     metadata: {
  //       confidence: 0.8,
  //       processingTime: 950
  //     }
  //   }
  // ];
  // therapistThoughts = [
  //   {
  //     content: 'Patient seems anxious. Need to establish rapport and create safe space. Using open-ended questions to encourage expression.',
  //     timestamp: new Date().toISOString()
  //   }
  // ];
  // patientThoughts = [
  //   {
  //     content: 'This feels awkward. Not sure what to expect. Should I trust this person? Maybe I should give this a chance.',
  //     timestamp: new Date().toISOString()
  //   }
  // ];
  
  // Configuration state
  let maxTurns = 20;
  let conversationActive = false;
  let conversationId: string | null = null;
  let selectedPersonaId: string | null = null;
  let showPersonaSelector = false;
  let showPatientDetailsModal = false;

  // Multi-therapist conversation state
  let isMultiTherapistMode = true; // Default to multi-therapist mode for comparative study
  let multiTherapistConversation: {
    cbtOnly: Array<{ sender: 'therapist' | 'patient'; content: string; timestamp: string; thinking?: string; metadata?: any; }>;
    miFixedPretreatment: Array<{ sender: 'therapist' | 'patient'; content: string; timestamp: string; thinking?: string; metadata?: any; }>;
    dynamicAdaptive: Array<{ sender: 'therapist' | 'patient'; content: string; timestamp: string; thinking?: string; metadata?: any; }>;
  } = {
    cbtOnly: [],
    miFixedPretreatment: [],
    dynamicAdaptive: []
  };

  let multiTherapistThoughts: {
    cbtOnly: Array<{ content: string; timestamp: string }>;
    miFixedPretreatment: Array<{ content: string; timestamp: string }>;
    dynamicAdaptive: Array<{ content: string; timestamp: string }>;
  } = {
    cbtOnly: [],
    miFixedPretreatment: [],
    dynamicAdaptive: []
  };

  // Evaluation state
  let currentEvaluation: CBTEvaluationResult | null = null;
  let currentComparativeEvaluation: ComparativeCBTEvaluationResult | null = null;
  let evaluationLoading = false;
  let evaluationError: string | null = null;

  // Bottom pane tab state
  let activeBottomTab = 'analytics'; // 'analytics' or 'evaluation'

  // Individual therapist analytics and evaluation state
  let cbtOnlyActiveTab = 'analytics'; // 'analytics' or 'evaluation'
  let miFixedActiveTab = 'analytics'; // 'analytics' or 'evaluation'
  let dynamicAdaptiveActiveTab = 'analytics'; // 'analytics' or 'evaluation'

  // Individual therapist analytics data
  let cbtOnlyAnalytics = {
    pre: { sentiment: null, motivationLevel: null, engagementLevel: null },
    post: { sentiment: null, motivationLevel: null, engagementLevel: null }
  };
  let miFixedAnalytics = {
    pre: { sentiment: null, motivationLevel: null, engagementLevel: null },
    post: { sentiment: null, motivationLevel: null, engagementLevel: null }
  };
  let dynamicAdaptiveAnalytics = {
    pre: { sentiment: null, motivationLevel: null, engagementLevel: null },
    post: { sentiment: null, motivationLevel: null, engagementLevel: null }
  };

  // Individual therapist evaluation data
  let cbtOnlyEvaluation: CBTEvaluationResult | null = null;
  let miFixedEvaluation: CBTEvaluationResult | null = null;
  let dynamicAdaptiveEvaluation: CBTEvaluationResult | null = null;
  
  onMount(() => {
    connectWebSocket();
    fetchTherapistProfile();

    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
    };
  });

  async function fetchTherapistProfile() {
    try {
      const response = await fetch('/api/therapist/profile');
      const result = await response.json();
      if (result.success) {
        therapistProfile = {
          name: result.data.name,
          credentials: result.data.credentials,
          specialization: result.data.specialization,
          approach: result.data.approach,
          experience: result.data.experience,
          sessions: result.data.sessions
        };
      }
    } catch (error) {
      console.error('Error fetching therapist profile:', error);
      // Keep default loading state if fetch fails
    }
  }

  function connectWebSocket() {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3000';
    wsConnection = new WebSocket(`${wsUrl}/ws`);
    
    wsConnection.onopen = () => {
      connectionStatus = 'Connected';
      console.log('🔌 WebSocket connected successfully');
    };

    wsConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📨 Received WebSocket message:', data);

        handleWebSocketMessage(data);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };
    
    wsConnection.onclose = () => {
      connectionStatus = 'Disconnected';
      console.log('WebSocket disconnected');
    };
    
    wsConnection.onerror = (error) => {
      connectionStatus = 'error';
      console.error('WebSocket error:', error);
    };
  }
  
  function handleWebSocketMessage(data: any) {
    console.log(`🔄 Handling message type: ${data.type}`);

    switch (data.type) {
      case 'welcome':
        console.log('👋 Welcome message received');
        break;

      case 'conversation_created':
        console.log('✅ Conversation created:', data.data);
        conversationId = data.data.conversationId;
        break;

      case 'multi_conversation_created':
        console.log('✅ Multi-therapist conversation created:', data.data);
        conversationId = data.data.conversationId;
        break;

      case 'conversation_started':
        console.log('🚀 Conversation started:', data.data);
        break;

      case 'multi_conversation_started':
        console.log('🚀 Multi-therapist conversation started:', data.data);
        break;

      case 'conversation_message':
        console.log('💬 Conversation message received:', data.data);
        handleConversationMessage(data.data);
        break;

      case 'multi_therapist_message':
        console.log('💬 Multi-therapist message received:', data.data);
        handleMultiTherapistMessage(data.data);
        break;

      case 'patient_message':
        console.log('💬 Patient message received:', data.data);
        handlePatientMessage(data.data);
        break;

      case 'conversation_ended':
        console.log('🏁 Conversation ended:', data.data);
        conversationActive = false;
        evaluationLoading = true;
        evaluationError = null;
        activeBottomTab = 'evaluation';
        console.log('messages:', messages);
        break;

      case 'multi_conversation_ended':
        console.log('🏁 Multi-therapist conversation ended:', data.data);
        conversationActive = false;
        evaluationLoading = true;
        evaluationError = null;
        activeBottomTab = 'evaluation';
        break;

      case 'comparative_evaluation_complete':
        console.log('📊 Comparative evaluation complete:', data.data);
        currentComparativeEvaluation = data.data;
        evaluationLoading = false;
        break;

      case 'conversation_paused':
        console.log('⏸️ Conversation paused');
        break;

      case 'conversation_resumed':
        console.log('▶️ Conversation resumed');
        break;

      case 'conversation_cleared':
        console.log('🗑️ Conversation cleared');
        messages = [];
        therapistThoughts = [];
        patientThoughts = [];
        currentEvaluation = null;
        evaluationLoading = false;
        evaluationError = null;
        activeBottomTab = 'analytics';
        break;

      case 'evaluation_completed':
        console.log('✅ Evaluation completed:', data.data);
        currentEvaluation = data.data.evaluation;
        evaluationLoading = false;
        evaluationError = null;
        break;

      case 'evaluation_error':
        console.error('❌ Evaluation error:', data.data);
        evaluationLoading = false;
        evaluationError = data.data.message || 'Evaluation failed';
        break;

      case 'error':
        console.error('❌ Server error:', data.message);
        break;

      default:
        console.warn('⚠️ Unknown message type:', data.type);
        // For backward compatibility, treat unknown messages as general messages
        if (data.message) {
          messages = [...messages, {
            sender: 'therapist', // Default sender
            content: data.message,
            timestamp: data.timestamp || new Date().toISOString()
          }];
        }
    }
  }

  function handleConversationMessage(messageData: any) {
    console.log('📝 Processing conversation message:', messageData);

    if (messageData.message) {
      // Add conversation message
      messages = [...messages, {
        sender: messageData.message.sender,
        content: messageData.message.content,
        timestamp: messageData.message.timestamp,
        thinking: messageData.thinking?.content,
        metadata: messageData.metadata
      }];

      // Update analytics based on therapist's analysis of patient
      if (messageData.message.sender === 'therapist' && messageData.metadata?.patientAnalysis) {
        const analysis = messageData.metadata.patientAnalysis;

        // Set pre-conversation analytics from second therapist message
        if (messages.length === 3) {
          preConversationAnalytics = {
            sentiment: analysis.sentiment,
            motivationLevel: analysis.motivationLevel,
            engagementLevel: analysis.engagementLevel
          };
        }

        // Always update post-conversation analytics with latest analysis
        postConversationAnalytics = {
          sentiment: analysis.sentiment,
          motivationLevel: analysis.motivationLevel,
          engagementLevel: analysis.engagementLevel
        };
      }

      console.log(`💭 ${messageData.message.sender} said: "${messageData.message.content}"`);
    }

    if (messageData.thinking) {
      // Add thinking to appropriate array
      const thought = {
        content: messageData.thinking.content,
        timestamp: messageData.thinking.timestamp
      };

      if (messageData.thinking.agent === 'therapist') {
        therapistThoughts = [...therapistThoughts, thought];
        console.log(`🧠 Therapist thinking: "${thought.content}"`);
      } else if (messageData.thinking.agent === 'patient') {
        patientThoughts = [...patientThoughts, thought];
        console.log(`💭 Patient thinking: "${thought.content}"`);
      }
    }
  }

  function handleMultiTherapistMessage(data: any) {
    console.log('📨 Processing multi-therapist message:', data);

    if (data.responses) {
      // Handle CBT-Only response
      if (data.responses.cbtOnly) {
        multiTherapistConversation.cbtOnly = [...multiTherapistConversation.cbtOnly, {
          sender: 'therapist',
          content: data.responses.cbtOnly.message.content,
          timestamp: data.responses.cbtOnly.message.timestamp,
          metadata: data.responses.cbtOnly.message.metadata
        }];

        if (data.responses.cbtOnly.thinking) {
          multiTherapistThoughts.cbtOnly = [...multiTherapistThoughts.cbtOnly, {
            content: data.responses.cbtOnly.thinking.content,
            timestamp: data.responses.cbtOnly.thinking.timestamp
          }];
        }

        // Update CBT-Only analytics
        if (data.responses.cbtOnly.message.metadata?.patientAnalysis) {
          const analysis = data.responses.cbtOnly.message.metadata.patientAnalysis;

          // Set pre-conversation analytics from second therapist message
          if (multiTherapistConversation.cbtOnly.length === 3) {
            cbtOnlyAnalytics.pre = {
              sentiment: analysis.sentiment,
              motivationLevel: analysis.motivationLevel,
              engagementLevel: analysis.engagementLevel
            };
          }

          // Always update post-conversation analytics with latest analysis
          cbtOnlyAnalytics.post = {
            sentiment: analysis.sentiment,
            motivationLevel: analysis.motivationLevel,
            engagementLevel: analysis.engagementLevel
          };
        }
      }

      // Handle MI Fixed Pretreatment response
      if (data.responses.miFixedPretreatment) {
        multiTherapistConversation.miFixedPretreatment = [...multiTherapistConversation.miFixedPretreatment, {
          sender: 'therapist',
          content: data.responses.miFixedPretreatment.message.content,
          timestamp: data.responses.miFixedPretreatment.message.timestamp,
          metadata: data.responses.miFixedPretreatment.message.metadata
        }];

        if (data.responses.miFixedPretreatment.thinking) {
          multiTherapistThoughts.miFixedPretreatment = [...multiTherapistThoughts.miFixedPretreatment, {
            content: data.responses.miFixedPretreatment.thinking.content,
            timestamp: data.responses.miFixedPretreatment.thinking.timestamp
          }];
        }

        // Update MI Fixed Pretreatment analytics
        if (data.responses.miFixedPretreatment.message.metadata?.patientAnalysis) {
          const analysis = data.responses.miFixedPretreatment.message.metadata.patientAnalysis;

          // Set pre-conversation analytics from second therapist message
          if (multiTherapistConversation.miFixedPretreatment.length === 3) {
            miFixedAnalytics.pre = {
              sentiment: analysis.sentiment,
              motivationLevel: analysis.motivationLevel,
              engagementLevel: analysis.engagementLevel
            };
          }

          // Always update post-conversation analytics with latest analysis
          miFixedAnalytics.post = {
            sentiment: analysis.sentiment,
            motivationLevel: analysis.motivationLevel,
            engagementLevel: analysis.engagementLevel
          };
        }
      }

      // Handle Dynamic Adaptive response
      if (data.responses.dynamicAdaptive) {
        multiTherapistConversation.dynamicAdaptive = [...multiTherapistConversation.dynamicAdaptive, {
          sender: 'therapist',
          content: data.responses.dynamicAdaptive.message.content,
          timestamp: data.responses.dynamicAdaptive.message.timestamp,
          metadata: data.responses.dynamicAdaptive.message.metadata
        }];

        if (data.responses.dynamicAdaptive.thinking) {
          multiTherapistThoughts.dynamicAdaptive = [...multiTherapistThoughts.dynamicAdaptive, {
            content: data.responses.dynamicAdaptive.thinking.content,
            timestamp: data.responses.dynamicAdaptive.thinking.timestamp
          }];
        }

        // Update Dynamic Adaptive analytics
        if (data.responses.dynamicAdaptive.message.metadata?.patientAnalysis) {
          const analysis = data.responses.dynamicAdaptive.message.metadata.patientAnalysis;

          // Set pre-conversation analytics from second therapist message
          if (multiTherapistConversation.dynamicAdaptive.length === 3) {
            dynamicAdaptiveAnalytics.pre = {
              sentiment: analysis.sentiment,
              motivationLevel: analysis.motivationLevel,
              engagementLevel: analysis.engagementLevel
            };
          }

          // Always update post-conversation analytics with latest analysis
          dynamicAdaptiveAnalytics.post = {
            sentiment: analysis.sentiment,
            motivationLevel: analysis.motivationLevel,
            engagementLevel: analysis.engagementLevel
          };
        }
      }

      // Update analytics from the dynamic adaptive response (as it's the most comprehensive)
      if (data.responses.dynamicAdaptive?.message?.metadata?.patientAnalysis) {
        const analysis = data.responses.dynamicAdaptive.message.metadata.patientAnalysis;
        postConversationAnalytics = {
          sentiment: analysis.sentiment,
          motivationLevel: analysis.motivationLevel,
          engagementLevel: analysis.engagementLevel
        };
      }
    }
  }

  function handlePatientMessage(data: any) {
    console.log('📨 Processing patient message:', data);

    if (data.message) {
      // Add patient message to all three therapist conversations
      const patientMessage = {
        sender: 'patient' as 'therapist' | 'patient',
        content: data.message.content,
        timestamp: data.message.timestamp
      };

      multiTherapistConversation.cbtOnly = [...multiTherapistConversation.cbtOnly, patientMessage];
      multiTherapistConversation.miFixedPretreatment = [...multiTherapistConversation.miFixedPretreatment, patientMessage];
      multiTherapistConversation.dynamicAdaptive = [...multiTherapistConversation.dynamicAdaptive, patientMessage];
    }

    if (data.thinking) {
      patientThoughts = [...patientThoughts, {
        content: data.thinking.content,
        timestamp: data.thinking.timestamp
      }];
    }
  }

  async function handlePersonaSelect(personaId: string) {
    selectedPersonaId = personaId;
    showPersonaSelector = false;
    console.log('🎭 Selected persona:', personaId);

    // Fetch persona details for display
    try {
      const response = await fetch(`/api/personas/${personaId}`);
      const result = await response.json();
      if (result.success) {
        selectedPersonaDetails = result.data;
        // Update patient profile display
        patientProfile = {
          name: result.data.name,
          age: result.data.age.toString(),
          sessionHistory: result.data.sessionContext,
          background: result.data.background,
          traits: result.data.conversationalStyle,
          lastSession: "Starting new session"
        };
      }
    } catch (error) {
      console.error('Error fetching persona details:', error);
    }
  }

  function startConversation() {
    console.log('🚀 Starting conversation...');
    console.log(`⚙️ Configuration: maxTurns=${maxTurns}, personaId=${selectedPersonaId}, multiTherapist=${isMultiTherapistMode}`);

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      // Clear evaluation data
      currentEvaluation = null;
      currentComparativeEvaluation = null;
      evaluationLoading = false;
      evaluationError = null;
      activeBottomTab = 'analytics';

      conversationActive = true;

      const messageType = isMultiTherapistMode ? 'start_multi_therapist_conversation' : 'start_conversation';
      const message = {
        type: messageType,
        config: {
          maxTurns,
          personaId: selectedPersonaId
        }
      };

      console.log('📤 Sending start conversation message:', message);
      wsConnection.send(JSON.stringify(message));
    } else {
      console.error('❌ WebSocket not connected');
    }
  }

  function clearConversation() {
    console.log('🗑️ Clearing conversation...');

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN && conversationId) {
      wsConnection.send(JSON.stringify({
        type: 'clear_conversation'
      }));
    }

    // Clear local state
    messages = [];
    therapistThoughts = [];
    patientThoughts = [];
    conversationActive = false;
    conversationId = null;

    // Reset multi-therapist state
    multiTherapistConversation = {
      cbtOnly: [],
      miFixedPretreatment: [],
      dynamicAdaptive: []
    };
    multiTherapistThoughts = {
      cbtOnly: [],
      miFixedPretreatment: [],
      dynamicAdaptive: []
    };

    // Reset analytics
    preConversationAnalytics = {
      sentiment: null,
      motivationLevel: null,
      engagementLevel: null
    };
    postConversationAnalytics = {
      sentiment: null,
      motivationLevel: null,
      engagementLevel: null
    };

    // Reset individual therapist analytics
    cbtOnlyAnalytics = {
      pre: { sentiment: null, motivationLevel: null, engagementLevel: null },
      post: { sentiment: null, motivationLevel: null, engagementLevel: null }
    };
    miFixedAnalytics = {
      pre: { sentiment: null, motivationLevel: null, engagementLevel: null },
      post: { sentiment: null, motivationLevel: null, engagementLevel: null }
    };
    dynamicAdaptiveAnalytics = {
      pre: { sentiment: null, motivationLevel: null, engagementLevel: null },
      post: { sentiment: null, motivationLevel: null, engagementLevel: null }
    };

    // Reset individual therapist evaluations
    cbtOnlyEvaluation = null;
    miFixedEvaluation = null;
    dynamicAdaptiveEvaluation = null;
  }
</script>

<svelte:head>
  <title>MiCA</title>
</svelte:head>

<div class="min-h-screen bg-neutral-50 dark:bg-neutral-900 theme-transition">
  <!-- Header -->
  <header class="bg-white dark:bg-neutral-800 shadow-sm border-b border-neutral-200 dark:border-neutral-700 theme-transition">
    <div class="mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <h1 class="text-2xl font-bold text-neutral-900 dark:text-neutral-100 theme-transition">MiCA</h1>
        </div>
        <div class="flex items-center space-x-4">
          <!-- Theme Toggle -->
          <ThemeToggle variant="button" size="md" />

          <!-- WebSocket Status -->
          <div class="flex items-center space-x-2">
            <span class="text-sm text-neutral-600 dark:text-neutral-400 theme-transition">WebSocket Status:</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {
              connectionStatus === 'Connected' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
              connectionStatus === 'Error' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :
              'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
            } theme-transition">
              {connectionStatus}
            </span>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Configuration Panel -->
  <div class="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 theme-transition">
    <div class="mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <label for="maxTurns" class="label">Max Turns:</label>
            <input
              id="maxTurns"
              type="number"
              bind:value={maxTurns}
              min="1"
              max="50"
              class="input w-16"
              disabled={conversationActive}
            />
          </div>
          <!-- <div class="flex items-center space-x-2">
            <button
              on:click={() => showPersonaSelector = !showPersonaSelector}
              disabled={conversationActive}
              class="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {selectedPersonaId ? 'Change Persona' : 'Select Persona'}
            </button>
          </div> -->
          <button
            on:click={() => showPersonaSelector = !showPersonaSelector}
            disabled={conversationActive}
            class="btn btn-secondary px-3 py-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
            {selectedPersonaId ? '🙎‍♂️ Change Patient' : '🙎‍♂️ Select Patient'}
          </button>
          {#if selectedPersonaDetails}
          <button
            on:click={() => showPatientDetailsModal = true}
            class="btn btn-secondary px-3 py-1"
            title="View detailed patient information"
            >
            🔍 View Patient
          </button>
          {/if}
          <!-- <div class="flex-1">
            <span class="profile-name">{patientProfile.name}</span>
          </div> -->
        </div>
        <div class="flex items-center space-x-2">
          <!-- Show spinner while conversationActive = true -->
          {#if conversationActive}
            <div role="status" class="text-center">
              <svg aria-hidden="true" class="inline w-6 h-6 text-neutral-300 dark:text-neutral-600 animate-spin fill-primary-600 dark:fill-primary-400 theme-transition" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
              </svg>
              <span class="sr-only">Loading...</span>
            </div>
          {:else if messages.length === 0}
            <div class="flex items-center justify-center h-full text-sm text-neutral-500 dark:text-neutral-400 theme-transition">
              <p>Click "Start" to begin...</p>
            </div>
          {/if}
          <button
            on:click={startConversation}
            disabled={conversationActive || connectionStatus !== 'Connected' || !selectedPersonaId}
            class="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {conversationActive ? 'Running...' : 'Start'}
          </button>
          <button
            on:click={clearConversation}
            disabled={conversationActive}
            class="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Persona Selector Modal -->
  {#if showPersonaSelector}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white dark:bg-neutral-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
              Select Patient
            </h2>
            <button
              on:click={() => showPersonaSelector = false}
              class="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <PersonaSelector
            {selectedPersonaId}
            onPersonaSelect={handlePersonaSelect}
          />
        </div>
      </div>
    </div>
  {/if}

  <!-- Patient Details Modal -->
  {#if showPatientDetailsModal && selectedPersonaDetails}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-neutral-100 dark:bg-neutral-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-semibold text-neutral-900 dark:text-neutral-100">
              Patient Details: {selectedPersonaDetails.name}
            </h2>
            <button
              on:click={() => showPatientDetailsModal = false}
              class="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Patient Overview -->
          <div class="mb-6 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-2">Basic Information</h3>
                <div class="space-y-1 text-sm text-neutral-800 dark:text-neutral-200">
                  <div><span class="font-semibold">Name:</span> {selectedPersonaDetails.name}</div>
                  <div><span class="font-semibold">Age:</span> {selectedPersonaDetails.age}</div>
                  <div><span class="font-semibold">Background:</span> {selectedPersonaDetails.background}</div>
                  <div><span class="font-semibold">Session Context:</span> {selectedPersonaDetails.sessionContext}</div>
                </div>
              </div>
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-2">Conversational Style</h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-300">{selectedPersonaDetails.conversationalStyle}</p>
              </div>
            </div>
          </div>

          <!-- Three-Column Layout -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <!-- Left Column: History & Situation -->
            <div class="space-y-6">
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  📋 Relevant History
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">
                  {selectedPersonaDetails.relevantHistory}
                </p>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🎯 Current Situation
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">
                  {selectedPersonaDetails.currentSituation}
                </p>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🎯 Presenting Concerns
                </h3>
                <div class="flex flex-wrap gap-2">
                  {#each selectedPersonaDetails.presentingConcerns as concern}
                    <span class="text-xs px-3 py-1 rounded-full bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800">
                      {concern}
                    </span>
                  {/each}
                </div>
              </div>
            </div>

            <!-- Middle Column: Cognitive Conceptualization -->
            <div class="space-y-6">
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🧠 Core Beliefs
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                  {#each selectedPersonaDetails.cognitiveConceptualization.coreBeliefs as belief}
                    <li class="flex items-start">
                      <span class="text-red-400 mr-2 mt-1">•</span>
                      <span class="italic">"{belief}"</span>
                    </li>
                  {/each}
                </ul>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🔗 Intermediate Beliefs
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                  {#each selectedPersonaDetails.cognitiveConceptualization.intermediateBeliefs as belief}
                    <li class="flex items-start">
                      <span class="text-orange-400 mr-2 mt-1">•</span>
                      <span class="italic">"{belief}"</span>
                    </li>
                  {/each}
                </ul>
              </div>

              {#if selectedPersonaDetails.cognitiveConceptualization.intermediateBeliefsDepression}
                <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                  <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                    😔 Depression-Related Beliefs
                  </h3>
                  <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                    {#each selectedPersonaDetails.cognitiveConceptualization.intermediateBeliefsDepression as belief}
                      <li class="flex items-start">
                        <span class="text-blue-400 mr-2 mt-1">•</span>
                        <span class="italic">"{belief}"</span>
                      </li>
                    {/each}
                  </ul>
                </div>
              {/if}

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🛠️ Coping Strategies
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                  {#each selectedPersonaDetails.cognitiveConceptualization.copingStrategies as strategy}
                    <li class="flex items-start">
                      <span class="text-green-400 mr-2 mt-1">•</span>
                      {strategy}
                    </li>
                  {/each}
                </ul>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🛡️ Coping Mechanisms
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                  {#each selectedPersonaDetails.behaviors.copingMechanisms as mechanism}
                    <li class="flex items-start">
                      <span class="text-blue-400 mr-2 mt-1">•</span>
                      {mechanism}
                    </li>
                  {/each}
                </ul>
              </div>
            </div>

            <!-- Right Column: Emotions & Behaviors -->
            <div class="space-y-6">
              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  💭 Automatic Thoughts
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-2">
                  {#each selectedPersonaDetails.automaticThoughts as thought}
                    <li class="flex items-start">
                      <span class="text-purple-400 mr-2 mt-1">•</span>
                      <span class="italic">"{thought}"</span>
                    </li>
                  {/each}
                </ul>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  😊 Emotions
                </h3>
                <div class="space-y-3">
                  <div>
                    <span class="text-xs font-medium text-neutral-700 dark:text-neutral-300">Primary:</span>
                    <div class="flex flex-wrap gap-1 mt-1">
                      {#each selectedPersonaDetails.emotions.primary as emotion}
                        <span class="text-xs px-2 py-1 rounded-full bg-neutral-200 dark:bg-neutral-600 text-neutral-700 dark:text-neutral-300">
                          {emotion}
                          {#if selectedPersonaDetails.emotions.intensityLevels?.[emotion]}
                            ({selectedPersonaDetails.emotions.intensityLevels[emotion]})
                          {/if}
                        </span>
                      {/each}
                    </div>
                  </div>
                  {#if selectedPersonaDetails.emotions.secondary}
                    <div>
                      <span class="text-xs font-medium text-neutral-700 dark:text-neutral-300">Secondary:</span>
                      <div class="flex flex-wrap gap-1 mt-1">
                        {#each selectedPersonaDetails.emotions.secondary as emotion}
                          <span class="text-xs px-2 py-1 rounded-full bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-400">
                            {emotion}
                          </span>
                        {/each}
                      </div>
                    </div>
                  {/if}
                </div>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  ⚠️ Maladaptive Behaviors
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                  {#each selectedPersonaDetails.behaviors.maladaptive as behavior}
                    <li class="flex items-start">
                      <span class="text-red-400 mr-2 mt-1">•</span>
                      {behavior}
                    </li>
                  {/each}
                </ul>
              </div>

              <div class="border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 rounded-lg p-3">
                <h3 class="font-semibold text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
                  🔄 Behavioral Patterns
                </h3>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                  {#each selectedPersonaDetails.behaviors.behavioralPatterns as pattern}
                    <li class="flex items-start">
                      <span class="text-yellow-400 mr-2 mt-1">•</span>
                      {pattern}
                    </li>
                  {/each}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/if}

  <!-- Main Content - Four Panes Layout -->
  <div class="mx-auto px-4 sm:px-6 lg:px-8 py-6 h-full flex flex-col">
    <!-- Top Section: Three Vertical Panes -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-1 min-h-0">
      
      <!-- Left Pane: Therapist Profile & Thinking -->
      <!-- <div class="card p-6 flex flex-col overflow-hidden"> -->
        <!-- Therapist Profile Section -->
        <!-- <div class="profile-section"> -->
          <!-- <div class="profile-header"> -->
            <!-- <div class="profile-avatar">
              {therapistProfile.name.split(' ').map(n => n[0]).join('')}
            </div> -->
            <!-- <div class="space-x-2"> -->
              <!-- <span class="text-4xl">👨‍⚕️</span> -->
              <!-- <span class="profile-name">{therapistProfile.name}</span> -->
              <!-- <p class="profile-title">{therapistProfile.credentials}</p> -->
            <!-- </div> -->
          <!-- </div> -->
          <!-- <div class="profile-details">
            <div class="profile-detail-item">
              <span class="profile-detail-label">Specialization:</span>
              <span class="profile-detail-value">{therapistProfile.specialization}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Approach:</span>
              <span class="profile-detail-value">{therapistProfile.approach}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Experience:</span>
              <span class="profile-detail-value">{therapistProfile.experience}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Sessions:</span>
              <span class="profile-detail-value">{therapistProfile.sessions}</span>
            </div>
          </div> -->
        <!-- </div> -->

        <!-- Therapist Thinking Section -->
        <!-- <div class="flex-1 flex flex-col">
          <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
            Therapist Thinking
          </h2>
          <div class="space-y-3 flex-1 overflow-y-auto">
          {#each therapistThoughts as thought}
            <div class="thinking-therapist dark:bg-primary-900/20 dark:text-blue-100 p-3 rounded-lg theme-transition">
              <p class="text-sm">{thought.content}</p>
              <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
              <div class="mt-2 text-xs">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if therapistThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500 dark:text-neutral-400 theme-transition">
              <p class="text-sm">Therapist thinking will appear here during conversation...</p>
            </div>
          {/if}
          </div>
        </div>
      </div> -->

      <!-- Middle Pane: Three-Therapist Conversations -->
      <div class="col-span-3 card flex flex-col overflow-hidden">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 flex items-center theme-transition">
            Comparative Therapist Study
          </h2>
          <div class="text-xs text-neutral-500 dark:text-neutral-400">
            Three therapeutic approaches responding to the same patient
          </div>
        </div>

        {#if isMultiTherapistMode}
          <!-- Three-Panel Therapist Layout -->
          <div class="grid grid-cols-3 gap-4 flex-1 min-h-0">

            <!-- CBT-Only Therapist Panel -->
            <div class="flex flex-col space-y-3">
              <!-- Conversation Section -->
              <div class="flex flex-col border border-neutral-200 dark:border-neutral-700 rounded-lg overflow-hidden h-[calc(100vh-550px)] min-h-48">
                <div class="bg-blue-50 dark:bg-blue-900/20 px-3 py-2 border-b border-neutral-200 dark:border-neutral-700">
                  <h3 class="text-sm font-semibold text-blue-700 dark:text-blue-300">CBT-Only Therapist</h3>
                  <p class="text-xs text-blue-600 dark:text-blue-400">Always uses CBT</p>
                </div>
                <div class="flex-1 overflow-y-auto p-3 space-y-3">
                  {#each multiTherapistConversation.cbtOnly as message}
                    <div class="flex flex-col space-y-1">
                      <div class="text-xs text-neutral-500 dark:text-neutral-400 flex items-center space-x-2">
                        <span class="font-medium {message.sender === 'therapist' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'}">
                          {message.sender === 'therapist' ? 'CBT Dr.' : `${patientProfile.name}`}
                        </span>
                        <span>•</span>
                        <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                      </div>
                      <div class="message-bubble {message.sender === 'therapist' ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500' : 'bg-gray-50 dark:bg-gray-900/20 border-l-4 border-gray-500'} text-neutral-900 dark:text-neutral-100 p-2 rounded-r-lg text-sm">
                        {#if message.metadata && message.metadata.patientAnalysis && message.sender === 'therapist'}
                          <!-- Patient Analysis Section -->
                          <div class="text-xs font-bold text-blue-600 dark:text-blue-400 mb-2">Patient Analysis:</div>
                          <div class="grid grid-cols-3 gap-2 mb-2 text-xs">
                            <!-- Sentiment -->
                            <div class="flex flex-col items-center">
                              <span class="font-bold text-blue-700 dark:text-blue-300">Sentiment</span>
                              <span class="px-2 py-1 rounded-full text-xs font-medium {
                                message.metadata.patientAnalysis.sentiment === 'positive' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                                message.metadata.patientAnalysis.sentiment === 'negative' ? 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300' :
                                'bg-gray-100 dark:bg-gray-900/50 text-gray-700 dark:text-gray-300'
                              }">
                                {message.metadata.patientAnalysis.sentiment}
                              </span>
                            </div>

                            <!-- Motivation -->
                            <div class="flex flex-col items-center">
                              <span class="font-bold text-blue-700 dark:text-blue-300">Motivation</span>
                              <span class="px-2 py-1 rounded-full text-xs font-medium {
                                message.metadata.patientAnalysis.motivationLevel === 'high' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                                message.metadata.patientAnalysis.motivationLevel === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                                'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                              }">
                                {message.metadata.patientAnalysis.motivationLevel}
                              </span>
                            </div>

                            <!-- Engagement -->
                            <div class="flex flex-col items-center">
                              <span class="font-bold text-blue-700 dark:text-blue-300">Engagement</span>
                              <span class="px-2 py-1 rounded-full text-xs font-medium {
                                message.metadata.patientAnalysis.engagementLevel === 'high' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                                message.metadata.patientAnalysis.engagementLevel === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                                'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                              }">
                                {message.metadata.patientAnalysis.engagementLevel}
                              </span>
                            </div>
                          </div>

                          <!-- Therapeutic Approach Section -->
                          {#if message.metadata.therapeuticApproach}
                            <div class="text-xs font-bold text-blue-600 dark:text-blue-400 mb-1">Therapeutic Approach:</div>
                            <div class="mb-2 space-y-1">
                              <div>
                                <span class="text-xs font-bold px-2 py-1 rounded bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300">
                                  {message.metadata.therapeuticApproach.name === 'Motivational Interviewing' ? 'MI' : 'CBT'} - {message.metadata.therapeuticApproach.name}
                                </span>
                              </div>
                              {#if message.metadata.therapeuticApproach.selectedTechnique}
                                <div>
                                  <span class="text-xs px-2 py-1 rounded bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-700">
                                    Technique: {message.metadata.therapeuticApproach.selectedTechnique.name}
                                  </span>
                                </div>
                              {/if}
                            </div>
                          {/if}

                          <!-- Divider -->
                          <hr class="border-blue-300 dark:border-blue-600 my-2" />
                        {/if}
                        {message.content}
                      </div>
                    </div>
                  {/each}

                  {#if conversationActive && multiTherapistConversation.cbtOnly.length === 0}
                    <div class="flex items-center justify-center h-full text-xs text-neutral-500 dark:text-neutral-400">
                      <p>Waiting for conversation to start...</p>
                    </div>
                  {/if}
                </div>
              </div>

              <!-- Analytics and Evaluation Section for CBT-Only -->
              <div class="border border-blue-200 dark:border-blue-800 rounded-lg overflow-hidden h-56">
                <!-- Tab Headers -->
                <div class="flex border-b border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
                  <button
                    class="px-3 py-2 text-xs font-medium border-b-2 transition-colors flex-1 {
                      cbtOnlyActiveTab === 'analytics'
                        ? 'border-blue-500 text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-800/30'
                        : 'border-transparent text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300'
                    }"
                    on:click={() => cbtOnlyActiveTab = 'analytics'}
                  >
                    Client States
                  </button>
                  <button
                    class="px-3 py-2 text-xs font-medium border-b-2 transition-colors flex-1 {
                      cbtOnlyActiveTab === 'evaluation'
                        ? 'border-blue-500 text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-800/30'
                        : 'border-transparent text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300'
                    }"
                    on:click={() => cbtOnlyActiveTab = 'evaluation'}
                  >
                    CBT Evaluation
                  </button>
                  <button
                    class="px-3 py-2 text-xs font-medium border-b-2 transition-colors flex-1 {
                      cbtOnlyActiveTab === 'misc'
                        ? 'border-blue-500 text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-800/30'
                        : 'border-transparent text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300'
                    }"
                  >
                    MISC
                  </button>
                </div>

                <!-- Tab Content -->
                <div class="flex-1 overflow-auto p-3">
                  {#if cbtOnlyActiveTab === 'analytics'}
                    <!-- CBT-Only Analytics -->
                    {#if cbtOnlyAnalytics.pre.sentiment || cbtOnlyAnalytics.post.sentiment}
                      <div class="grid grid-cols-2 gap-3 h-full">
                        <!-- Pre-Conversation Column -->
                        <div class="space-y-2">
                          <div class="text-xs font-semibold text-blue-700 dark:text-blue-300">Pre-Conversation</div>
                          <div class="space-y-1">
                            <div class="flex justify-between text-xs">
                              <span>Sentiment:</span>
                              <span class="font-medium {cbtOnlyAnalytics.pre.sentiment || 'neutral'}">{cbtOnlyAnalytics.pre.sentiment || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Motivation:</span>
                              <span class="font-medium {cbtOnlyAnalytics.pre.motivationLevel || 'neutral'}">{cbtOnlyAnalytics.pre.motivationLevel || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Engagement:</span>
                              <span class="font-medium {cbtOnlyAnalytics.pre.engagementLevel || 'neutral'}">{cbtOnlyAnalytics.pre.engagementLevel || 'N/A'}</span>
                            </div>
                          </div>
                        </div>

                        <!-- Post-Conversation Column -->
                        <div class="space-y-2">
                          <div class="text-xs font-semibold text-blue-700 dark:text-blue-300">Current State</div>
                          <div class="space-y-1">
                            <div class="flex justify-between text-xs">
                              <span>Sentiment:</span>
                              <span class="font-medium {cbtOnlyAnalytics.post.sentiment || 'neutral'}">{cbtOnlyAnalytics.post.sentiment || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Motivation:</span>
                              <span class="font-medium {cbtOnlyAnalytics.post.motivationLevel || 'neutral'}">{cbtOnlyAnalytics.post.motivationLevel || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Engagement:</span>
                              <span class="font-medium {cbtOnlyAnalytics.post.engagementLevel || 'neutral'}">{cbtOnlyAnalytics.post.engagementLevel || 'N/A'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    <!-- {:else}
                      <div class="flex items-center justify-center h-full">
                        <p class="text-xs text-neutral-500 dark:text-neutral-400">Analytics will appear during conversation...</p>
                      </div> -->
                    {/if}
                  <!-- {:else}
                    <div class="flex items-center justify-center h-full">
                      <p class="text-xs text-neutral-500 dark:text-neutral-400">Individual evaluation coming soon...</p>
                    </div> -->
                  {/if}
                </div>
              </div>
            </div>

            <!-- MI Fixed Pretreatment Therapist Panel -->
            <div class="flex flex-col space-y-3">
              <!-- Conversation Section -->
              <div class="flex flex-col border border-neutral-200 dark:border-neutral-700 rounded-lg overflow-hidden h-[calc(100vh-550px)] min-h-48">
                <div class="bg-green-50 dark:bg-green-900/20 px-3 py-2 border-b border-neutral-200 dark:border-neutral-700">
                  <h3 class="text-sm font-semibold text-green-700 dark:text-green-300">MI Fixed Pretreatment</h3>
                  <p class="text-xs text-green-600 dark:text-green-400">MI → CBT</p>
                </div>
                <div class="flex-1 overflow-y-auto p-3 space-y-3">
                  {#each multiTherapistConversation.miFixedPretreatment as message}
                    <div class="flex flex-col space-y-1">
                      <div class="text-xs text-neutral-500 dark:text-neutral-400 flex items-center space-x-2">
                        <span class="font-medium {message.sender === 'therapist' ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}">
                          {message.sender === 'therapist' ? 'MI-Fixed Dr.' : `${patientProfile.name}`}
                        </span>
                        <span>•</span>
                        <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                      </div>
                      <div class="message-bubble {message.sender === 'therapist' ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500' : 'bg-gray-50 dark:bg-gray-900/20 border-l-4 border-gray-500'} text-neutral-900 dark:text-neutral-100 p-2 rounded-r-lg text-sm">
                        {#if message.metadata && message.metadata.patientAnalysis && message.sender === 'therapist'}
                          <!-- Patient Analysis Section -->
                          <div class="text-xs font-bold text-green-600 dark:text-green-400 mb-2">Patient Analysis:</div>
                          <div class="grid grid-cols-3 gap-2 mb-2 text-xs">
                            <!-- Sentiment -->
                            <div class="flex flex-col items-center">
                              <span class="font-bold text-green-700 dark:text-green-300">Sentiment</span>
                              <span class="px-2 py-1 rounded-full text-xs font-medium {
                                message.metadata.patientAnalysis.sentiment === 'positive' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                                message.metadata.patientAnalysis.sentiment === 'negative' ? 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300' :
                                'bg-gray-100 dark:bg-gray-900/50 text-gray-700 dark:text-gray-300'
                              }">
                                {message.metadata.patientAnalysis.sentiment}
                              </span>
                            </div>

                            <!-- Motivation -->
                            <div class="flex flex-col items-center">
                              <span class="font-bold text-green-700 dark:text-green-300">Motivation</span>
                              <span class="px-2 py-1 rounded-full text-xs font-medium {
                                message.metadata.patientAnalysis.motivationLevel === 'high' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                                message.metadata.patientAnalysis.motivationLevel === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                                'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                              }">
                                {message.metadata.patientAnalysis.motivationLevel}
                              </span>
                            </div>

                            <!-- Engagement -->
                            <div class="flex flex-col items-center">
                              <span class="font-bold text-green-700 dark:text-green-300">Engagement</span>
                              <span class="px-2 py-1 rounded-full text-xs font-medium {
                                message.metadata.patientAnalysis.engagementLevel === 'high' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                                message.metadata.patientAnalysis.engagementLevel === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                                'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                              }">
                                {message.metadata.patientAnalysis.engagementLevel}
                              </span>
                            </div>
                          </div>

                          <!-- Therapeutic Approach Section -->
                          {#if message.metadata.therapeuticApproach}
                            <div class="text-xs font-bold text-green-600 dark:text-green-400 mb-1">Therapeutic Approach:</div>
                            <div class="mb-2 space-y-1">
                              <div>
                                <span class="text-xs font-bold px-2 py-1 rounded bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300">
                                  {message.metadata.therapeuticApproach.name === 'Motivational Interviewing' ? 'MI' : 'CBT'} - {message.metadata.therapeuticApproach.name}
                                </span>
                              </div>
                              {#if message.metadata.therapeuticApproach.selectedTechnique}
                                <div>
                                  <span class="text-xs px-2 py-1 rounded bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 border border-green-200 dark:border-green-700">
                                    Technique: {message.metadata.therapeuticApproach.selectedTechnique.name}
                                  </span>
                                </div>
                              {/if}
                            </div>
                          {/if}

                          <!-- Divider -->
                          <hr class="border-green-300 dark:border-green-600 my-2" />
                        {/if}
                        {message.content}
                      </div>
                    </div>
                  {/each}

                  {#if conversationActive && multiTherapistConversation.miFixedPretreatment.length === 0}
                    <div class="flex items-center justify-center h-full text-xs text-neutral-500 dark:text-neutral-400">
                      <p>Waiting for conversation to start...</p>
                    </div>
                  {/if}
                </div>
              </div>

              <!-- Analytics and Evaluation Section for MI Fixed Pretreatment -->
              <div class="border border-green-200 dark:border-green-800 rounded-lg overflow-hidden h-56">
                <!-- Tab Headers -->
                <div class="flex border-b border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
                  <button
                    class="px-3 py-2 text-xs font-medium border-b-2 transition-colors flex-1 {
                      miFixedActiveTab === 'analytics'
                        ? 'border-green-500 text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-800/30'
                        : 'border-transparent text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300'
                    }"
                    on:click={() => miFixedActiveTab = 'analytics'}
                  >
                    Client States
                  </button>
                  <button
                    class="px-3 py-2 text-xs font-medium border-b-2 transition-colors flex-1 {
                      miFixedActiveTab === 'evaluation'
                        ? 'border-green-500 text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-800/30'
                        : 'border-transparent text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300'
                    }"
                    on:click={() => miFixedActiveTab = 'evaluation'}
                  >
                    CBT Evaluation
                  </button>
                  <button
                    class="px-3 py-2 text-xs font-medium border-b-2 transition-colors flex-1 {
                      miFixedActiveTab === 'misc'
                        ? 'border-green-500 text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-800/30'
                        : 'border-transparent text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300'
                    }"
                  >
                    MISC
                  </button>
                </div>

                <!-- Tab Content -->
                <div class="flex-1 overflow-hidden p-3">
                  {#if miFixedActiveTab === 'analytics'}
                    <!-- MI Fixed Analytics -->
                    {#if miFixedAnalytics.pre.sentiment || miFixedAnalytics.post.sentiment}
                      <div class="grid grid-cols-2 gap-3 h-full">
                        <!-- Pre-Conversation Column -->
                        <div class="space-y-2">
                          <div class="text-xs font-semibold text-green-700 dark:text-green-300">Pre-Conversation</div>
                          <div class="space-y-1">
                            <div class="flex justify-between text-xs">
                              <span>Sentiment:</span>
                              <span class="font-medium {miFixedAnalytics.pre.sentiment || 'neutral'}">{miFixedAnalytics.pre.sentiment || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Motivation:</span>
                              <span class="font-medium {miFixedAnalytics.pre.motivationLevel || 'neutral'}">{miFixedAnalytics.pre.motivationLevel || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Engagement:</span>
                              <span class="font-medium {miFixedAnalytics.pre.engagementLevel || 'neutral'}">{miFixedAnalytics.pre.engagementLevel || 'N/A'}</span>
                            </div>
                          </div>
                        </div>

                        <!-- Post-Conversation Column -->
                        <div class="space-y-2">
                          <div class="text-xs font-semibold text-green-700 dark:text-green-300">Current State</div>
                          <div class="space-y-1">
                            <div class="flex justify-between text-xs">
                              <span>Sentiment:</span>
                              <span class="font-medium {miFixedAnalytics.post.sentiment || 'neutral'}">{miFixedAnalytics.post.sentiment || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Motivation:</span>
                              <span class="font-medium {miFixedAnalytics.post.motivationLevel || 'neutral'}">{miFixedAnalytics.post.motivationLevel || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Engagement:</span>
                              <span class="font-medium {miFixedAnalytics.post.engagementLevel || 'neutral'}">{miFixedAnalytics.post.engagementLevel || 'N/A'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    <!-- {:else}
                      <div class="flex items-center justify-center h-full">
                        <p class="text-xs text-neutral-500 dark:text-neutral-400">Analytics will appear during conversation...</p>
                      </div> -->
                    {/if}
                  <!-- {:else}
                    <div class="flex items-center justify-center h-full">
                      <p class="text-xs text-neutral-500 dark:text-neutral-400">Individual evaluation coming soon...</p>
                    </div> -->
                  {/if}
                </div>
              </div>
            </div>

            <!-- Dynamic Adaptive Therapist Panel -->
            <div class="flex flex-col space-y-3">
              <!-- Conversation Section -->
              <div class="flex flex-col border border-neutral-200 dark:border-neutral-700 rounded-lg overflow-hidden h-[calc(100vh-550px)] min-h-48">
                <div class="bg-purple-50 dark:bg-purple-900/20 px-3 py-2 border-b border-neutral-200 dark:border-neutral-700">
                  <h3 class="text-sm font-semibold text-purple-700 dark:text-purple-300">Dynamic Adaptive</h3>
                  <p class="text-xs text-purple-600 dark:text-purple-400">Real-time MI ↔ CBT</p>
                </div>
                <div class="flex-1 overflow-y-auto p-3 space-y-3">
                  {#each multiTherapistConversation.dynamicAdaptive as message}
                    <div class="flex flex-col space-y-1">
                      <div class="text-xs text-neutral-500 dark:text-neutral-400 flex items-center space-x-2">
                        <span class="font-medium {message.sender === 'therapist' ? 'text-purple-600 dark:text-purple-400' : 'text-gray-600 dark:text-gray-400'}">
                          {message.sender === 'therapist' ? 'Dynamic Dr.' : `${patientProfile.name}`}
                        </span>
                        <span>•</span>
                        <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                      </div>
                      <div class="message-bubble {message.sender === 'therapist' ? 'bg-purple-50 dark:bg-purple-900/20 border-l-4 border-purple-500' : 'bg-gray-50 dark:bg-gray-900/20 border-l-4 border-gray-500'} text-neutral-900 dark:text-neutral-100 p-2 rounded-r-lg text-sm">
                        {#if message.metadata && message.metadata.patientAnalysis && message.sender === 'therapist'}
                          <!-- Patient Analysis Section -->
                          <div class="text-xs font-bold text-purple-600 dark:text-purple-400 mb-2">Patient Analysis:</div>
                          <div class="grid grid-cols-3 gap-2 mb-2 text-xs">
                            <!-- Sentiment -->
                            <div class="flex flex-col items-center">
                              <span class="font-bold text-purple-700 dark:text-purple-300">Sentiment</span>
                              <span class="px-2 py-1 rounded-full text-xs font-medium {
                                message.metadata.patientAnalysis.sentiment === 'positive' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                                message.metadata.patientAnalysis.sentiment === 'negative' ? 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300' :
                                'bg-gray-100 dark:bg-gray-900/50 text-gray-700 dark:text-gray-300'
                              }">
                                {message.metadata.patientAnalysis.sentiment}
                              </span>
                            </div>

                            <!-- Motivation -->
                            <div class="flex flex-col items-center">
                              <span class="font-bold text-purple-700 dark:text-purple-300">Motivation</span>
                              <span class="px-2 py-1 rounded-full text-xs font-medium {
                                message.metadata.patientAnalysis.motivationLevel === 'high' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                                message.metadata.patientAnalysis.motivationLevel === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                                'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                              }">
                                {message.metadata.patientAnalysis.motivationLevel}
                              </span>
                            </div>

                            <!-- Engagement -->
                            <div class="flex flex-col items-center">
                              <span class="font-bold text-purple-700 dark:text-purple-300">Engagement</span>
                              <span class="px-2 py-1 rounded-full text-xs font-medium {
                                message.metadata.patientAnalysis.engagementLevel === 'high' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                                message.metadata.patientAnalysis.engagementLevel === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                                'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                              }">
                                {message.metadata.patientAnalysis.engagementLevel}
                              </span>
                            </div>
                          </div>

                          <!-- Therapeutic Approach Section -->
                          {#if message.metadata.therapeuticApproach}
                            <div class="text-xs font-bold text-purple-600 dark:text-purple-400 mb-1">Therapeutic Approach:</div>
                            <div class="mb-2 space-y-1">
                              <div>
                                <span class="text-xs font-bold px-2 py-1 rounded bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300">
                                  {message.metadata.therapeuticApproach.name === 'Motivational Interviewing' ? 'MI' : 'CBT'} - {message.metadata.therapeuticApproach.name}
                                </span>
                              </div>
                              {#if message.metadata.therapeuticApproach.selectedTechnique}
                                <div>
                                  <span class="text-xs px-2 py-1 rounded bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 border border-purple-200 dark:border-purple-700">
                                    Technique: {message.metadata.therapeuticApproach.selectedTechnique.name}
                                  </span>
                                </div>
                              {/if}
                            </div>
                          {/if}

                          <!-- Divider -->
                          <hr class="border-purple-300 dark:border-purple-600 my-2" />
                        {/if}
                        {message.content}
                      </div>
                    </div>
                  {/each}

                  {#if conversationActive && multiTherapistConversation.dynamicAdaptive.length === 0}
                    <div class="flex items-center justify-center h-full text-xs text-neutral-500 dark:text-neutral-400">
                      <p>Waiting for conversation to start...</p>
                    </div>
                  {/if}
                </div>
              </div>

              <!-- Analytics and Evaluation Section for Dynamic Adaptive -->
              <div class="border border-purple-200 dark:border-purple-800 rounded-lg overflow-hidden h-56">
                <!-- Tab Headers -->
                <div class="flex border-b border-purple-200 dark:border-purple-800 bg-purple-50 dark:bg-purple-900/20">
                  <button
                    class="px-3 py-2 text-xs font-medium border-b-2 transition-colors flex-1 {
                      dynamicAdaptiveActiveTab === 'analytics'
                        ? 'border-purple-500 text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-800/30'
                        : 'border-transparent text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300'
                    }"
                    on:click={() => dynamicAdaptiveActiveTab = 'analytics'}
                  >
                    Client States
                  </button>
                  <button
                    class="px-3 py-2 text-xs font-medium border-b-2 transition-colors flex-1 {
                      dynamicAdaptiveActiveTab === 'evaluation'
                        ? 'border-purple-500 text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-800/30'
                        : 'border-transparent text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300'
                    }"
                    on:click={() => dynamicAdaptiveActiveTab = 'evaluation'}
                  >
                    CBT Evaluation
                  </button>
                  <button
                    class="px-3 py-2 text-xs font-medium border-b-2 transition-colors flex-1 {
                      cbtOnlyActiveTab === 'misc'
                        ? 'border-purple-500 text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-800/30'
                        : 'border-transparent text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300'
                    }"
                  >
                    MISC
                  </button>
                </div>

                <!-- Tab Content -->
                <div class="flex-1 overflow-hidden p-3">
                  {#if dynamicAdaptiveActiveTab === 'analytics'}
                    <!-- Dynamic Adaptive Analytics -->
                    {#if dynamicAdaptiveAnalytics.pre.sentiment || dynamicAdaptiveAnalytics.post.sentiment}
                      <div class="grid grid-cols-2 gap-3 h-full">
                        <!-- Pre-Conversation Column -->
                        <div class="space-y-2">
                          <div class="text-xs font-semibold text-purple-700 dark:text-purple-300">Pre-Conversation</div>
                          <div class="space-y-1">
                            <div class="flex justify-between text-xs">
                              <span>Sentiment:</span>
                              <span class="font-medium {dynamicAdaptiveAnalytics.pre.sentiment || 'neutral'}">{dynamicAdaptiveAnalytics.pre.sentiment || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Motivation:</span>
                              <span class="font-medium {dynamicAdaptiveAnalytics.pre.motivationLevel || 'neutral'}">{dynamicAdaptiveAnalytics.pre.motivationLevel || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Engagement:</span>
                              <span class="font-medium {dynamicAdaptiveAnalytics.pre.engagementLevel || 'neutral'}">{dynamicAdaptiveAnalytics.pre.engagementLevel || 'N/A'}</span>
                            </div>
                          </div>
                        </div>

                        <!-- Post-Conversation Column -->
                        <div class="space-y-2">
                          <div class="text-xs font-semibold text-purple-700 dark:text-purple-300">Current State</div>
                          <div class="space-y-1">
                            <div class="flex justify-between text-xs">
                              <span>Sentiment:</span>
                              <span class="font-medium {dynamicAdaptiveAnalytics.post.sentiment || 'neutral'}">{dynamicAdaptiveAnalytics.post.sentiment || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Motivation:</span>
                              <span class="font-medium {dynamicAdaptiveAnalytics.post.motivationLevel || 'neutral'}">{dynamicAdaptiveAnalytics.post.motivationLevel || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Engagement:</span>
                              <span class="font-medium {dynamicAdaptiveAnalytics.post.engagementLevel || 'neutral'}">{dynamicAdaptiveAnalytics.post.engagementLevel || 'N/A'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    <!-- {:else}
                      <div class="flex items-center justify-center h-full">
                        <p class="text-xs text-neutral-500 dark:text-neutral-400">Analytics will appear during conversation...</p>
                      </div> -->
                    {/if}
                  <!-- {:else}
                    <div class="flex items-center justify-center h-full">
                      <p class="text-xs text-neutral-500 dark:text-neutral-400">Individual evaluation coming soon...</p>
                    </div> -->
                  {/if}
                </div>
              </div>
            </div>

          </div>
        {:else}
          <!-- Single Therapist Mode (Fallback) -->
          <div class="space-y-4 flex-1 overflow-y-auto">
            {#each messages as message}
              <div class="flex flex-col space-y-1">
                <div class="text-xs text-neutral-500 dark:text-neutral-400 flex items-center space-x-2 theme-transition">
                  <span class="font-medium capitalize {message.sender === 'therapist' ? 'text-primary-600 dark:text-primary-400' : 'text-secondary-600 dark:text-secondary-400'} theme-transition">
                    {message.sender === 'therapist' ? '👨‍⚕️ Dr. Sila' : `👤 ${patientProfile.name}`}
                  </span>
                  <span>•</span>
                  <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                </div>
              <div class="message-bubble {message.sender === 'therapist' ? 'bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary-500' : 'bg-secondary-50 dark:bg-secondary-900/20 border-l-4 border-secondary-500'} text-neutral-900 dark:text-neutral-100 p-3 rounded-r-lg theme-transition">
                {#if message.metadata && message.metadata.patientAnalysis && message.sender === 'therapist'}
                  <!-- Patient Analysis Section -->
                  <div class="text-xs font-bold text-primary-600 dark:text-primary-400 mb-2">Patient Analysis:</div>
                  <div class="grid grid-cols-3 gap-2 mb-2 text-xs">
                    <!-- Sentiment -->
                    <div class="flex flex-col items-center">
                      <span class="font-bold text-primary-700 dark:text-primary-300">Sentiment</span>
                      <span class="px-2 py-1 rounded-full text-xs font-medium {
                        message.metadata.patientAnalysis.sentiment === 'positive' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                        message.metadata.patientAnalysis.sentiment === 'negative' ? 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300' :
                        'bg-gray-100 dark:bg-gray-900/50 text-gray-700 dark:text-gray-300'
                      }">
                        {message.metadata.patientAnalysis.sentiment}
                      </span>
                    </div>

                    <!-- Motivation -->
                    <div class="flex flex-col items-center">
                      <span class="font-bold text-primary-700 dark:text-primary-300">Motivation</span>
                      <span class="px-2 py-1 rounded-full text-xs font-medium {
                        message.metadata.patientAnalysis.motivationLevel === 'high' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                        message.metadata.patientAnalysis.motivationLevel === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                        'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                      }">
                        {message.metadata.patientAnalysis.motivationLevel}
                      </span>
                    </div>

                    <!-- Engagement -->
                    <div class="flex flex-col items-center">
                      <span class="font-bold text-primary-700 dark:text-primary-300">Engagement</span>
                      <span class="px-2 py-1 rounded-full text-xs font-medium {
                        message.metadata.patientAnalysis.engagementLevel === 'high' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                        message.metadata.patientAnalysis.engagementLevel === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                        'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
                      }">
                        {message.metadata.patientAnalysis.engagementLevel}
                      </span>
                    </div>
                  </div>

                  <!-- Therapeutic Approach Section -->
                  {#if message.metadata.therapeuticApproach}
                    <div class="text-xs font-bold text-primary-600 dark:text-primary-400 mb-1">Therapeutic Approach:</div>
                    <div class="mb-2 space-y-1">
                      <div>
                        <span class="text-xs font-bold px-2 py-1 rounded bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300">
                          {message.metadata.therapeuticApproach.name === 'Motivational Interviewing' ? 'MI' : 'CBT'} - {message.metadata.therapeuticApproach.name}
                        </span>
                      </div>
                      {#if message.metadata.therapeuticApproach.selectedTechnique}
                        <div>
                          <span class="text-xs px-2 py-1 rounded bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 border border-primary-200 dark:border-primary-700">
                            Technique: {message.metadata.therapeuticApproach.selectedTechnique.name}
                          </span>
                        </div>
                      {/if}
                    </div>
                  {/if}

                  <!-- Divider -->
                  <hr class="border-primary-300 dark:border-primary-600 my-2" />
                {/if}
                {message.content}
              </div>
            </div>
          {/each}
          </div>
        {/if}
      </div>

      <!-- Right Pane: Patient Profile & Thinking -->
      <!-- <div class="card p-6 flex flex-col overflow-hidden"> -->
        <!-- Patient Profile Section -->
        <!-- <div class="profile-section">
          <div class="profile-header space-x-2 items-end">
            <div class="profile-avatar patient">
              {patientProfile.name.split(' ').map(n => n[0]).join('')}
            </div>
            <div class="flex-1 space-x-2">
              <span class="text-4xl">🤷‍♂️</span>
              <span class="profile-name">{patientProfile.name}</span>
            </div>
            {#if selectedPersonaDetails}
              <button
                on:click={() => showPatientDetailsModal = true}
                class="btn btn-secondary text-xs px-3 py-1"
                title="View detailed patient information"
              >
                View Persona
              </button>
            {/if}
            <button
              on:click={() => showPersonaSelector = !showPersonaSelector}
              disabled={conversationActive}
              class="btn btn-secondary text-xs px-3 py-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {selectedPersonaId ? 'Change Patient' : 'Select Patient'}
            </button>
          </div>
          <div class="profile-details">
            <div class="profile-detail-item">
              <span class="profile-detail-label">Session:</span>
              <span class="profile-detail-value">{patientProfile.sessionHistory}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Focus Area:</span>
              <span class="profile-detail-value">{patientProfile.background}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Traits:</span>
              <span class="profile-detail-value">{patientProfile.traits}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Last Session:</span>
              <span class="profile-detail-value">{patientProfile.lastSession}</span>
            </div>
          </div>
        </div> -->

        <!-- Patient Thinking Section -->
        <!-- <div class="flex-1 flex flex-col">
          <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
            Patient Thinking
          </h2>
          <div class="space-y-3 flex-1 overflow-y-auto">
          {#each patientThoughts as thought}
            <div class="thinking-patient dark:bg-secondary-900/20 dark:text-purple-100 p-3 rounded-lg theme-transition">
              <p class="text-sm">{thought.content}</p>
              <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
              <div class="mt-2 text-xs">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if patientThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500 dark:text-neutral-400 theme-transition">
              <p class="text-sm">Patient thinking will appear here during conversation...</p>
            </div>
          {/if}
          </div>
        </div> -->
      <!-- </div> -->

    </div>

    <!-- Bottom Pane: Tabbed Analytics and Evaluation -->
    <!-- <div class="card p-6 bottom-pane flex flex-col overflow-hidden">
      <div class="flex border-b border-neutral-200 dark:border-neutral-700 mb-4">
        <button
          class="px-4 py-2 text-sm font-medium border-b-2 transition-colors {
            activeBottomTab === 'analytics'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300'
          }"
          on:click={() => activeBottomTab = 'analytics'}
        >
          Client State Analytics
        </button>
        <button
          class="px-4 py-2 text-sm font-medium border-b-2 transition-colors {
            activeBottomTab === 'evaluation'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300'
          }"
          on:click={() => activeBottomTab = 'evaluation'}
        >
          CBT Evaluation (COCOA)
        </button>
      </div>

      <div class="flex-1 overflow-hidden">
        {#if activeBottomTab === 'analytics'}
          <div class="h-full overflow-y-auto">
            <div class="analytics-section h-full">
              <div class="analytics-header">
                Client State Analytics
              </div>
              {#if preConversationAnalytics.sentiment || postConversationAnalytics.sentiment}
                <div class="analytics-grid">
                  <div class="analytics-column">
                    <div class="analytics-column-title">Pre-Conversation</div>
                    <div class="analytics-metric">
                      <span class="analytics-metric-label">Sentiment</span>
                      <span class="analytics-metric-value {preConversationAnalytics.sentiment || 'neutral'}">
                        {preConversationAnalytics.sentiment || 'N/A'}
                      </span>
                    </div>
                    <div class="analytics-metric">
                      <span class="analytics-metric-label">Motivation</span>
                      <span class="analytics-metric-value {preConversationAnalytics.motivationLevel || 'neutral'}">
                        {preConversationAnalytics.motivationLevel || 'N/A'}
                      </span>
                    </div>
                    <div class="analytics-metric">
                      <span class="analytics-metric-label">Engagement</span>
                      <span class="analytics-metric-value {preConversationAnalytics.engagementLevel || 'neutral'}">
                        {preConversationAnalytics.engagementLevel || 'N/A'}
                      </span>
                    </div>
                  </div>

                  <div class="analytics-column">
                    <div class="analytics-column-title">Current State</div>
                    <div class="analytics-metric">
                      <span class="analytics-metric-label">Sentiment</span>
                      <span class="analytics-metric-value {postConversationAnalytics.sentiment || 'neutral'}">
                        {postConversationAnalytics.sentiment || 'N/A'}
                      </span>
                    </div>
                    <div class="analytics-metric">
                      <span class="analytics-metric-label">Motivation</span>
                      <span class="analytics-metric-value {postConversationAnalytics.motivationLevel || 'neutral'}">
                        {postConversationAnalytics.motivationLevel || 'N/A'}
                      </span>
                    </div>
                    <div class="analytics-metric">
                      <span class="analytics-metric-label">Engagement</span>
                      <span class="analytics-metric-value {postConversationAnalytics.engagementLevel || 'neutral'}">
                        {postConversationAnalytics.engagementLevel || 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
              {:else}
                <div class="text-center py-8">
                  <p class="text-neutral-500 dark:text-neutral-500 text-sm">
                    Analytics will appear here during conversation...
                  </p>
                </div>
              {/if}
            </div>
          </div>
        {:else}
          <div class="h-full overflow-y-auto">
            <div class="evaluation-section h-full">
              {#if isMultiTherapistMode && currentComparativeEvaluation}
                <div class="space-y-6">
                  <div class="text-center">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                      Comparative Therapist Evaluation Results
                    </h3>
                    <p class="text-sm text-neutral-600 dark:text-neutral-400">
                      CBT effectiveness comparison across three therapeutic strategies
                    </p>
                  </div>

                  <div class="grid grid-cols-3 gap-4">
                    <div class="text-center p-4 border border-blue-200 dark:border-blue-800 rounded-lg {currentComparativeEvaluation.comparison.bestPerforming === 'cbt-only' ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-400' : ''}">
                      <h4 class="font-semibold text-blue-700 dark:text-blue-300 mb-2">CBT-Only</h4>
                      <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {currentComparativeEvaluation.comparison.scoreComparison.cbtOnly.toFixed(1)}
                      </div>
                      <div class="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                        Overall Score
                      </div>
                    </div>

                    <div class="text-center p-4 border border-green-200 dark:border-green-800 rounded-lg {currentComparativeEvaluation.comparison.bestPerforming === 'mi-fixed-pretreatment' ? 'bg-green-50 dark:bg-green-900/20 border-green-400' : ''}">
                      <h4 class="font-semibold text-green-700 dark:text-green-300 mb-2">MI Fixed Pretreatment</h4>
                      <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {currentComparativeEvaluation.comparison.scoreComparison.miFixedPretreatment.toFixed(1)}
                      </div>
                      <div class="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                        Overall Score
                      </div>
                    </div>

                    <div class="text-center p-4 border border-purple-200 dark:border-purple-800 rounded-lg {currentComparativeEvaluation.comparison.bestPerforming === 'dynamic-adaptive' ? 'bg-purple-50 dark:bg-purple-900/20 border-purple-400' : ''}">
                      <h4 class="font-semibold text-purple-700 dark:text-purple-300 mb-2">Dynamic Adaptive</h4>
                      <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {currentComparativeEvaluation.comparison.scoreComparison.dynamicAdaptive.toFixed(1)}
                      </div>
                      <div class="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                        Overall Score
                      </div>
                    </div>
                  </div>

                  <div class="text-center p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                    <h4 class="font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                      🏆 Best Performing Strategy
                    </h4>
                    <div class="text-lg font-medium text-primary-600 dark:text-primary-400">
                      {currentComparativeEvaluation.comparison.bestPerforming === 'cbt-only' ? 'CBT-Only Therapist' :
                       currentComparativeEvaluation.comparison.bestPerforming === 'mi-fixed-pretreatment' ? 'MI Fixed Pretreatment' :
                       'Dynamic Adaptive Therapist'}
                    </div>
                  </div>

                  {#if currentComparativeEvaluation.comparison.insights.length > 0}
                    <div class="space-y-2">
                      <h4 class="font-semibold text-neutral-900 dark:text-neutral-100">Key Insights</h4>
                      <ul class="space-y-1">
                        {#each currentComparativeEvaluation.comparison.insights as insight}
                          <li class="text-sm text-neutral-600 dark:text-neutral-400 flex items-start">
                            <span class="text-primary-500 mr-2">•</span>
                            {insight}
                          </li>
                        {/each}
                      </ul>
                    </div>
                  {/if}
                </div>
              {:else}
                <EvaluationDisplay
                  evaluation={currentEvaluation}
                  isLoading={evaluationLoading}
                  error={evaluationError}
                />
              {/if}
            </div>
          </div>
        {/if}
      </div>
    </div> -->
  </div>
</div>

<style>
  .evaluation-section {
    padding-top: 1rem;
    flex: 1;
    overflow: hidden;
  }

  /* Bottom pane specific styles */
  .bottom-pane {
    height: 20rem; /* 320px - fixed height for bottom pane */
  }
</style>
